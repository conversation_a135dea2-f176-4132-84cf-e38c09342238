"""
诊断PNConstellation模型的问题
特别关注revenue_rate计算、station任务处理、资源约束等
"""
import torch
import numpy as np
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from constellation_smp.constellation_smp import ConstellationSMPDataset, reward
from train_multi_constellation_modes import create_model_for_mode
from hyperparameter import args

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

def analyze_dataset():
    """分析数据集中的station任务和收益分布"""
    print("=" * 60)
    print("1. 数据集分析")
    print("=" * 60)
    
    # 创建测试数据集
    dataset = ConstellationSMPDataset(
        size=10,
        num_samples=3,
        seed=12345,
        memory_total=0.3,
        power_total=5,
        num_satellites=3
    )
    
    static, dynamic, _ = dataset[0]
    
    print(f"Static数据形状: {static.shape}")
    print(f"Dynamic数据形状: {dynamic.shape}")
    print()
    
    # 分析static数据
    print("Static数据分析:")
    print(f"  任务开始时间 (static[0]): {static[0, :5]}")
    print(f"  任务位置 (static[1]): {static[1, :5]}")
    print(f"  任务结束时间 (static[2]): {static[2, :5]}")
    print(f"  任务持续时间 (static[3]): {static[3, :5]}")
    print(f"  任务收益 (static[4]): {static[4, :5]}")
    print(f"  内存消耗 (static[5]): {static[5, :5]}")
    print(f"  能量消耗 (static[6]): {static[6, :5]}")
    
    if static.shape[0] > 7:  # 有station标记
        print(f"  Station标记 (static[7]): {static[7, :5]}")
        station_indices = torch.where(static[7, :] == 1)[0]
        print(f"  Station任务索引: {station_indices.tolist()}")
        
        if len(station_indices) > 0:
            for idx in station_indices:
                print(f"    Station {idx}: 收益={static[4, idx]:.3f}, 内存消耗={static[5, idx]:.3f}")
    
    # 计算总收益
    total_revenue = torch.sum(static[4, :])
    print(f"  总可能收益: {total_revenue:.3f}")
    print()
    
    return static, dynamic

def test_single_task_execution():
    """测试单个任务执行的资源更新"""
    print("=" * 60)
    print("2. 单任务执行测试")
    print("=" * 60)
    
    dataset = ConstellationSMPDataset(
        size=5,
        num_samples=1,
        seed=12345,
        memory_total=0.3,
        power_total=5,
        num_satellites=3
    )
    
    static, dynamic, _ = dataset[0]
    static = static.unsqueeze(0)  # 添加batch维度
    dynamic = dynamic.unsqueeze(0)
    
    print("初始状态:")
    print(f"  卫星0内存: {dynamic[0, 2, :, 0]}")
    print(f"  卫星1内存: {dynamic[0, 2, :, 1]}")
    print(f"  卫星2内存: {dynamic[0, 2, :, 2]}")
    print()
    
    # 模拟执行任务1（假设是普通任务）
    chosen_idx = torch.tensor([1])
    satellite_idx = torch.tensor([0])
    
    print(f"执行任务 {chosen_idx[0]} (卫星 {satellite_idx[0]}):")
    print(f"  任务收益: {static[0, 4, chosen_idx[0]]:.3f}")
    print(f"  内存消耗: {static[0, 5, chosen_idx[0]]:.3f}")
    print(f"  能量消耗: {static[0, 6, chosen_idx[0]]:.3f}")
    
    # 检查是否是station任务
    if static.shape[1] > 7:
        is_station = static[0, 7, chosen_idx[0]] == 1
        print(f"  是否为Station: {is_station.item()}")
    
    # 更新动态状态
    new_dynamic = dataset.update_dynamic(static, dynamic, chosen_idx, satellite_idx)
    
    print("更新后状态:")
    print(f"  卫星0内存: {new_dynamic[0, 2, :, 0]}")
    print(f"  卫星1内存: {new_dynamic[0, 2, :, 1]}")
    print(f"  卫星2内存: {new_dynamic[0, 2, :, 2]}")
    print()
    
    return static, dynamic, new_dynamic

def test_reward_calculation():
    """测试奖励计算逻辑"""
    print("=" * 60)
    print("3. 奖励计算测试")
    print("=" * 60)
    
    dataset = ConstellationSMPDataset(
        size=5,
        num_samples=1,
        seed=12345,
        memory_total=0.3,
        power_total=5,
        num_satellites=3
    )
    
    static, dynamic, _ = dataset[0]
    static = static.unsqueeze(0)
    
    # 模拟一个简单的任务序列
    tour_indices = torch.tensor([[0, 1, 2, 3]])  # 执行任务1,2,3
    satellite_indices = torch.tensor([[0, 0, 1, 2]])  # 分别由卫星0,0,1,2执行
    
    print("模拟任务序列:")
    for i in range(1, tour_indices.size(1)):
        task_idx = tour_indices[0, i]
        sat_idx = satellite_indices[0, i]
        task_revenue = static[0, 4, task_idx]
        print(f"  任务{task_idx} -> 卫星{sat_idx}, 收益: {task_revenue:.3f}")
    
    # 计算奖励
    reward_val, revenue_rate, distance, memory, power = reward(
        static, tour_indices, satellite_indices, 'cooperative'
    )
    
    print(f"\n奖励计算结果:")
    print(f"  总奖励: {reward_val[0]:.3f}")
    print(f"  收益率: {revenue_rate[0]:.3f}")
    print(f"  总距离: {distance[0]:.3f}")
    print(f"  总内存使用: {memory[0]:.3f}")
    print(f"  总能量使用: {power[0]:.3f}")
    
    # 手动计算预期收益率
    executed_revenue = torch.sum(static[0, 4, tour_indices[0, 1:]])
    total_possible_revenue = torch.sum(static[0, 4, :])
    expected_revenue_rate = executed_revenue / total_possible_revenue
    
    print(f"\n手动验证:")
    print(f"  执行任务收益: {executed_revenue:.3f}")
    print(f"  总可能收益: {total_possible_revenue:.3f}")
    print(f"  预期收益率: {expected_revenue_rate:.3f}")
    print(f"  实际收益率: {revenue_rate[0]:.3f}")
    print(f"  差异: {abs(expected_revenue_rate - revenue_rate[0]):.6f}")
    
    return reward_val, revenue_rate

def test_model_forward():
    """测试PNConstellation模型的前向传播"""
    print("=" * 60)
    print("4. 模型前向传播测试")
    print("=" * 60)
    
    dataset = ConstellationSMPDataset(
        size=5,
        num_samples=2,
        seed=12345,
        memory_total=0.3,
        power_total=5,
        num_satellites=3
    )
    
    static1, dynamic1, _ = dataset[0]
    static2, dynamic2, _ = dataset[1]
    static = torch.stack([static1, static2], dim=0).to(device)
    dynamic = torch.stack([dynamic1, dynamic2], dim=0).to(device)
    
    print(f"输入形状: static={static.shape}, dynamic={dynamic.shape}")
    
    try:
        # 创建PNConstellation模型
        actor, critic = create_model_for_mode(
            'cooperative', 
            dataset, 
            model_type='pn',
            rnn_type='indrnn',
            use_transformer=False
        )
        
        actor.eval()
        critic.eval()
        
        with torch.no_grad():
            tour_indices, satellite_indices, tour_logp, satellite_logp = actor(static, dynamic)
            critic_value = critic(static, dynamic)
        
        print(f"✓ 前向传播成功")
        print(f"  tour_indices: {tour_indices.shape}")
        print(f"  satellite_indices: {satellite_indices.shape}")
        print(f"  critic_value: {critic_value.shape}")
        
        # 分析任务分配
        print(f"\n任务分配分析 (batch 0):")
        for i in range(1, min(tour_indices.size(1), 6)):  # 只看前5个任务
            task_idx = tour_indices[0, i].item()
            sat_idx = satellite_indices[0, i].item()
            print(f"  步骤{i}: 任务{task_idx} -> 卫星{sat_idx}")
        
        # 统计每颗卫星执行的任务数
        sat_task_counts = torch.zeros(3)
        for i in range(1, tour_indices.size(1)):
            sat_idx = satellite_indices[0, i].item()
            if sat_idx < 3:  # 确保索引有效
                sat_task_counts[sat_idx] += 1
        
        print(f"\n卫星任务分配统计:")
        for i in range(3):
            print(f"  卫星{i}: {sat_task_counts[i].item()}个任务")
        
        # 计算奖励
        reward_val, revenue_rate, distance, memory, power = reward(
            static, tour_indices, satellite_indices, 'cooperative'
        )
        
        print(f"\n性能指标:")
        print(f"  奖励: {reward_val[0]:.3f}")
        print(f"  收益率: {revenue_rate[0]:.3f}")
        print(f"  距离: {distance[0]:.3f}")
        print(f"  内存: {memory[0]:.3f}")
        print(f"  能量: {power[0]:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 前向传播失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主诊断函数"""
    print("🔍 PNConstellation模型诊断开始...")
    print()
    
    # 1. 数据集分析
    static, dynamic = analyze_dataset()
    
    # 2. 单任务执行测试
    test_single_task_execution()
    
    # 3. 奖励计算测试
    test_reward_calculation()
    
    # 4. 模型前向传播测试
    model_success = test_model_forward()
    
    print("=" * 60)
    print("诊断总结")
    print("=" * 60)
    print(f"数据集分析: ✅ 完成")
    print(f"单任务执行测试: ✅ 完成")
    print(f"奖励计算测试: ✅ 完成")
    print(f"模型前向传播测试: {'✅ 成功' if model_success else '❌ 失败'}")
    
    if model_success:
        print("\n🎉 基础功能测试通过，可以进行详细问题分析")
    else:
        print("\n⚠️ 基础功能存在问题，需要优先修复")

if __name__ == "__main__":
    main()
