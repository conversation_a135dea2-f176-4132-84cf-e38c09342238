"""
PN+IndRNN模型问题诊断脚本
系统性分析PN+IndRNN模型在训练过程中出现的问题
"""
import os
import sys
import torch
import numpy as np
import time
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from constellation_smp.constellation_smp import ConstellationSMPDataset, reward, render
from constellation_smp.pn_constellation import PNConstellation, ConstellationStateCriticPN
from constellation_smp.gpn_constellation import GPNConstellation, ConstellationStateCritic
from hyperparameter import args

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

def analyze_model_architecture():
    """分析模型架构差异"""
    print("🔍 模型架构对比分析")
    print("=" * 60)
    
    # 创建测试数据
    test_data = ConstellationSMPDataset(50, 10, 12345, 0.3, 5, 3)
    test_loader = DataLoader(test_data, batch_size=2, shuffle=False)
    static, dynamic, _ = next(iter(test_loader))
    
    static = static.to(device)
    dynamic = dynamic.to(device)
    
    print(f"输入数据维度:")
    print(f"  static: {static.shape}")
    print(f"  dynamic: {dynamic.shape}")
    
    # 创建PN模型
    pn_actor = PNConstellation(
        static_size=static.size(1),
        dynamic_size=dynamic.size(1),
        hidden_size=128,
        num_satellites=3,
        rnn='indrnn',
        num_layers=2,
        constellation_mode='cooperative'
    ).to(device)
    
    # 创建GPN模型
    gpn_actor = GPNConstellation(
        static_size=static.size(1),
        dynamic_size=dynamic.size(1),
        hidden_size=128,
        num_satellites=3,
        rnn='indrnn',
        num_layers=2,
        constellation_mode='cooperative',
        use_transformer=False  # 为了公平对比，不使用Transformer
    ).to(device)
    
    # 参数数量对比
    pn_params = sum(p.numel() for p in pn_actor.parameters())
    gpn_params = sum(p.numel() for p in gpn_actor.parameters())
    
    print(f"\n参数数量对比:")
    print(f"  PN模型: {pn_params:,}")
    print(f"  GPN模型: {gpn_params:,}")
    print(f"  差异: {gpn_params - pn_params:,} ({(gpn_params/pn_params-1)*100:.1f}%)")
    
    return pn_actor, gpn_actor, static, dynamic

def analyze_forward_pass_time():
    """分析前向传播时间"""
    print("\n🕐 前向传播时间分析")
    print("=" * 60)
    
    pn_actor, gpn_actor, static, dynamic = analyze_model_architecture()
    
    # 预热
    with torch.no_grad():
        for _ in range(5):
            pn_actor(static, dynamic)
            gpn_actor(static, dynamic)
    
    # 测试PN模型
    pn_times = []
    for _ in range(10):
        start_time = time.time()
        with torch.no_grad():
            pn_actor(static, dynamic)
        pn_times.append(time.time() - start_time)
    
    # 测试GPN模型
    gpn_times = []
    for _ in range(10):
        start_time = time.time()
        with torch.no_grad():
            gpn_actor(static, dynamic)
        gpn_times.append(time.time() - start_time)
    
    pn_avg = np.mean(pn_times) * 1000  # 转换为毫秒
    gpn_avg = np.mean(gpn_times) * 1000
    
    print(f"前向传播时间 (平均):")
    print(f"  PN模型: {pn_avg:.2f}ms")
    print(f"  GPN模型: {gpn_avg:.2f}ms")
    print(f"  PN模型慢了: {pn_avg/gpn_avg:.2f}倍")
    
    if pn_avg > gpn_avg * 2:
        print("⚠️ PN模型前向传播时间异常，可能存在效率问题")
    
    return pn_actor, gpn_actor, static, dynamic

def analyze_output_distribution():
    """分析输出分布"""
    print("\n📊 输出分布分析")
    print("=" * 60)
    
    pn_actor, gpn_actor, static, dynamic = analyze_forward_pass_time()
    
    # 多次采样分析输出分布
    pn_rewards = []
    gpn_rewards = []
    pn_revenue_rates = []
    gpn_revenue_rates = []
    
    for i in range(20):
        # PN模型
        with torch.no_grad():
            pn_tour, pn_sat, _, _ = pn_actor(static, dynamic)
            pn_reward, pn_revenue, pn_dist, pn_mem, pn_pow = reward(static, pn_tour, pn_sat, 'cooperative')
            pn_rewards.append(pn_reward.mean().item())
            pn_revenue_rates.append(pn_revenue.mean().item())
        
        # GPN模型
        with torch.no_grad():
            gpn_tour, gpn_sat, _, _ = gpn_actor(static, dynamic)
            gpn_reward, gpn_revenue, gpn_dist, gpn_mem, gpn_pow = reward(static, gpn_tour, gpn_sat, 'cooperative')
            gpn_rewards.append(gpn_reward.mean().item())
            gpn_revenue_rates.append(gpn_revenue.mean().item())
    
    print(f"奖励分布统计:")
    print(f"  PN模型 - 均值: {np.mean(pn_rewards):.3f}, 标准差: {np.std(pn_rewards):.3f}")
    print(f"  GPN模型 - 均值: {np.mean(gpn_rewards):.3f}, 标准差: {np.std(gpn_rewards):.3f}")
    
    print(f"\n收益率分布统计:")
    print(f"  PN模型 - 均值: {np.mean(pn_revenue_rates):.3f}, 标准差: {np.std(pn_revenue_rates):.3f}")
    print(f"  GPN模型 - 均值: {np.mean(gpn_revenue_rates):.3f}, 标准差: {np.std(gpn_revenue_rates):.3f}")
    
    # 检查异常值
    if np.mean(pn_revenue_rates) > 0.8:
        print("🔴 PN模型收益率异常高，可能存在约束违反问题")
    if np.std(pn_rewards) > np.std(gpn_rewards) * 2:
        print("🔴 PN模型输出不稳定，方差过大")

def analyze_satellite_selection_pattern():
    """分析卫星选择模式"""
    print("\n🛰️ 卫星选择模式分析")
    print("=" * 60)
    
    pn_actor, gpn_actor, static, dynamic = analyze_model_architecture()
    
    # 分析卫星选择分布
    pn_sat_counts = np.zeros(3)
    gpn_sat_counts = np.zeros(3)
    
    for i in range(50):
        # PN模型
        with torch.no_grad():
            pn_tour, pn_sat, _, _ = pn_actor(static, dynamic)
            for sat_idx in pn_sat.flatten():
                pn_sat_counts[sat_idx.item()] += 1
        
        # GPN模型
        with torch.no_grad():
            gpn_tour, gpn_sat, _, _ = gpn_actor(static, dynamic)
            for sat_idx in gpn_sat.flatten():
                gpn_sat_counts[sat_idx.item()] += 1
    
    # 归一化
    pn_sat_dist = pn_sat_counts / pn_sat_counts.sum()
    gpn_sat_dist = gpn_sat_counts / gpn_sat_counts.sum()
    
    print(f"卫星选择分布:")
    print(f"  PN模型: {pn_sat_dist}")
    print(f"  GPN模型: {gpn_sat_dist}")
    
    # 计算均衡度 (标准差，越小越均衡)
    pn_balance = np.std(pn_sat_dist)
    gpn_balance = np.std(gpn_sat_dist)
    
    print(f"\n卫星选择均衡度 (标准差):")
    print(f"  PN模型: {pn_balance:.4f}")
    print(f"  GPN模型: {gpn_balance:.4f}")
    
    if pn_balance > 0.2:
        print("🔴 PN模型卫星选择严重不均衡")
    if pn_balance > gpn_balance * 2:
        print("🔴 PN模型卫星选择比GPN模型更不均衡")

def main():
    """主函数"""
    print("🚨 PN+IndRNN模型问题诊断")
    print("=" * 80)
    
    try:
        # 1. 模型架构分析
        analyze_model_architecture()
        
        # 2. 前向传播时间分析
        analyze_forward_pass_time()
        
        # 3. 输出分布分析
        analyze_output_distribution()
        
        # 4. 卫星选择模式分析
        analyze_satellite_selection_pattern()
        
        print("\n" + "=" * 80)
        print("📋 诊断总结")
        print("=" * 80)
        print("1. 检查模型架构和参数数量差异")
        print("2. 分析前向传播效率问题")
        print("3. 验证输出分布的合理性")
        print("4. 评估卫星选择的均衡性")
        print("\n基于以上分析结果，可以确定PN+IndRNN模型的具体问题所在。")
        
    except Exception as e:
        print(f"❌ 诊断过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
