"""
定义星座任务规划的PN模型
基于Pointer Network的星座任务规划模型，专门处理4维dynamic数据
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from gpn import Encoder
from attention.attention import MultiHead_Additive_Attention, Additive_Attention_Glimpse
from indrnn.indrnn import IndRNN, IndRNN_Net, IndRNNv2
from constellation_smp.gpn_constellation import ConstellationEncoder

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')


class ConstellationPointer(nn.Module):
    """
    星座任务规划的Pointer模块，基于PN4SMP的Pointer但适配星座级别的特征
    """
    def __init__(self, hidden_size, num_layers=1, dropout=0.2,
                 attention='MultiHead_Additive_Attention', n_head=8,
                 rnn='indrnn', num_nodes=50):
        super(ConstellationPointer, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # RNN选择
        if rnn == 'gru':
            self.gru = nn.GRU(hidden_size, hidden_size, num_layers,
                              batch_first=True,
                              dropout=dropout if num_layers > 1 else 0)
        elif rnn == 'lstm':
            self.gru = nn.LSTM(hidden_size, hidden_size, num_layers,
                               batch_first=True,
                               dropout=dropout if num_layers > 1 else 0)
        elif rnn == 'indrnn':
            self.gru = IndRNN_Net(hidden_size, hidden_size,
                                  num_nodes, num_layers, IndRNN)
        elif rnn == 'indrnnv2':
            self.gru = IndRNN_Net(hidden_size, hidden_size,
                                  num_nodes, num_layers, IndRNNv2)

        # 注意力机制
        if attention == 'MultiHead_Additive_Attention':
            self.encoder_attn = MultiHead_Additive_Attention(hidden_size, n_head)
        elif attention == 'Additive_Attention_Glimpse':
            self.encoder_attn = Additive_Attention_Glimpse(hidden_size)

        self.drop_rnn = nn.Dropout(p=dropout)
        self.drop_hh = nn.Dropout(p=dropout)

    def forward(self, static_hidden, dynamic_hidden, decoder_hidden, last_hh):
        """
        static_hidden: (batch_size, hidden_size, seq_len) - 静态特征
        dynamic_hidden: (batch_size, hidden_size, seq_len) - 动态特征
        decoder_hidden: (batch_size, hidden_size, 1) - 解码器隐藏状态
        last_hh: RNN的隐藏状态

        返回:
        probs: (batch_size, seq_len) - 任务选择概率
        last_hh: 更新后的RNN隐藏状态
        """
        # RNN处理
        rnn_out, last_hh = self.gru(decoder_hidden.transpose(2, 1), last_hh)
        rnn_out = self.drop_rnn(rnn_out.squeeze(1))

        # 使用注意力机制计算任务选择概率
        if hasattr(self, 'encoder_attn'):
            if isinstance(self.encoder_attn, Additive_Attention_Glimpse):
                # Additive_Attention_Glimpse: 3个参数 (static_hidden, dynamic_hidden, decoder_hidden)
                probs = self.encoder_attn(static_hidden, dynamic_hidden, rnn_out)
            else:
                # MultiHead_Additive_Attention: 2个参数 (q, context)
                # q: (batch_size, hidden_size) - rnn_out
                # context: (batch_size * seq_len, hidden_size) - 合并的特征

                # 合并static和dynamic特征
                combined_features = static_hidden + dynamic_hidden  # (batch_size, hidden_size, seq_len)
                # 重塑为正确的格式: (batch_size * seq_len, hidden_size)
                batch_size, hidden_size, seq_len = combined_features.shape
                combined_flat = combined_features.transpose(1, 2).contiguous().view(batch_size * seq_len, hidden_size)

                # 调用注意力机制
                probs, _ = self.encoder_attn(rnn_out, combined_flat)  # 返回 (probs, context)
        else:
            # 如果没有注意力机制，使用简单的线性层
            probs = torch.randn(rnn_out.size(0), static_hidden.size(2), device=rnn_out.device)

        return probs, last_hh


class PNConstellation(nn.Module):
    """
    基于Pointer Network的星座任务规划模型
    专门处理星座任务的4维dynamic数据: (batch_size, dynamic_size, seq_len, num_satellites)
    """
    def __init__(self, static_size, dynamic_size, hidden_size, num_satellites,
                 rnn='indrnn', num_layers=2, update_fn=None, mask_fn=None,
                 num_nodes=50, dropout=0.1, constellation_mode='cooperative',
                 attention='MultiHead_Additive_Attention', n_head=8):
        super(PNConstellation, self).__init__()
        
        if dynamic_size < 1:
            raise ValueError(':param dynamic_size: must be > 0, even if the '
                             'problem has no dynamic elements')
        
        self.static_size = static_size
        self.dynamic_size = dynamic_size
        self.hidden_size = hidden_size
        self.num_satellites = num_satellites
        self.update_fn = update_fn
        self.mask_fn = mask_fn
        self.constellation_mode = constellation_mode
        self.num_layers = num_layers
        self.dropout = dropout
        
        # 复用ConstellationEncoder处理多卫星数据
        self.constellation_encoder = ConstellationEncoder(
            static_size + dynamic_size,
            hidden_size,
            num_satellites,
            constellation_mode,
            use_transformer=False,  # PN模型不使用Transformer
            transformer_config=None
        )
        
        # 解码器，用于处理当前选择的任务
        self.decoder = Encoder(static_size, hidden_size)
        
        # 任务选择的Pointer网络
        self.task_pointer = ConstellationPointer(
            hidden_size, num_layers, dropout,
            attention, n_head, rnn, num_nodes
        )

        # 为了兼容PN的设计，我们需要单独的编码器
        self.static_encoder = Encoder(static_size, hidden_size)
        self.dynamic_encoder = Encoder(dynamic_size, hidden_size)
        
        # 卫星选择器（决定哪颗卫星执行任务）
        self.satellite_selector = nn.Sequential(
            nn.Linear(hidden_size * 2, hidden_size),
            nn.LayerNorm(hidden_size),
            nn.GELU(),
            nn.Linear(hidden_size, num_satellites)
        )
        
        # 初始化参数
        for p in self.parameters():
            if len(p.shape) > 1:
                nn.init.xavier_uniform_(p)
        
        # 初始解码器输入
        self.x0 = torch.zeros((1, static_size, 1), requires_grad=True, device=device)
        
        print(f"PNConstellation: {num_satellites} satellites, {constellation_mode} mode")
        print(f"RNN: {rnn}, layers: {num_layers}")
        print(f"Attention: {attention}, heads: {n_head}")

    def forward(self, static, dynamic):
        """
        static: (batch_size, static_size, seq_len)
        dynamic: (batch_size, dynamic_size, seq_len, num_satellites)
        
        返回:
        tour_indices: (batch_size, tour_len) - 选择的任务索引
        satellite_indices: (batch_size, tour_len) - 执行任务的卫星索引
        tour_logp: (batch_size, tour_len) - 任务选择的对数概率
        satellite_logp: (batch_size, tour_len) - 卫星选择的对数概率
        """
        batch_size = static.size(0)
        seq_len = static.size(2)
        
        # 使用ConstellationEncoder处理多卫星数据
        constellation_features, satellite_features = self.constellation_encoder(static, dynamic)

        # 同时也需要为PN风格的处理准备static和dynamic的编码
        static_hidden = self.static_encoder(static)
        # 对于dynamic，我们需要将4维数据转换为3维，这里使用平均或者选择第一个卫星
        # 为了简化，我们使用所有卫星的平均dynamic状态
        dynamic_avg = dynamic.mean(dim=3)  # (batch_size, dynamic_size, seq_len)
        dynamic_hidden = self.dynamic_encoder(dynamic_avg)

        # 初始化解码器输入
        decoder_input = self.x0.expand(batch_size, -1, -1)
        decoder_hidden = self.decoder(decoder_input)
        
        # 初始化RNN隐藏状态
        last_hh = None
        
        # 初始化结果列表
        tour_indices = []
        satellite_indices = []
        tour_logp = []
        satellite_logp = []
        
        # 第一个任务是起始点
        first_idx = torch.zeros(batch_size, dtype=torch.long, device=static.device)
        first_sat_idx = torch.zeros(batch_size, dtype=torch.long, device=static.device)
        
        tour_indices.append(first_idx)
        satellite_indices.append(first_sat_idx)
        tour_logp.append(torch.zeros(batch_size, device=static.device))
        satellite_logp.append(torch.zeros(batch_size, device=static.device))
        
        # 获取初始掩码
        if self.mask_fn is not None:
            mask_result = self.mask_fn(dynamic, static=static)
            if isinstance(mask_result, tuple):
                mask, satellite_masks = mask_result
            else:
                mask = mask_result
                satellite_masks = None
        else:
            mask = torch.zeros(batch_size, seq_len, device=static.device)
            mask[:, 0] = 1  # 只有起始点可以选择
        
        # 使用固定的步数，与GPNConstellation保持一致
        max_steps = seq_len - 1  # 第一个任务已经添加，所以是seq_len-1

        for step in range(max_steps):
            
            # 使用Pointer网络选择任务
            task_probs, last_hh = self.task_pointer(
                static_hidden, dynamic_hidden, decoder_hidden, last_hh
            )
            
            # 应用掩码 - 使用更稳定的掩码方式
            # 将不可选择的位置设为很大的负数
            masked_logits = task_probs.clone()
            masked_logits[mask == 0] = -1e9
            task_probs = F.softmax(masked_logits, dim=1)
            
            # 任务选择
            if self.training:
                # 检查概率是否有效
                if torch.isnan(task_probs).any() or torch.isinf(task_probs).any():
                    print(f"Warning: Invalid probabilities detected at step {step}")
                    print(f"task_probs: {task_probs}")
                    print(f"mask: {mask}")
                    # 使用均匀分布作为后备
                    valid_mask = mask > 0
                    task_probs = valid_mask.float()
                    task_probs = task_probs / task_probs.sum(dim=1, keepdim=True)

                task_dist = torch.distributions.Categorical(task_probs)
                task_idx = task_dist.sample()
                task_logp = task_dist.log_prob(task_idx)
            else:
                # 贪婪选择
                _, task_idx = torch.max(task_probs, 1)
                task_logp = torch.log(torch.gather(task_probs, 1, task_idx.unsqueeze(1))).squeeze(1)
            
            # 获取选定任务的特征用于卫星选择
            task_features = torch.gather(
                constellation_features,
                2,
                task_idx.unsqueeze(1).unsqueeze(2).expand(-1, constellation_features.size(1), 1)
            ).squeeze(2)
            
            # 卫星选择
            # 结合任务特征和当前解码器状态
            sat_input = torch.cat([task_features, decoder_hidden.squeeze(2)], dim=1)
            sat_logits = self.satellite_selector(sat_input)
            
            # 应用卫星掩码（如果有的话）
            if self.mask_fn is not None and satellite_masks is not None:
                # 获取当前任务对应的卫星掩码
                sat_mask = satellite_masks[torch.arange(batch_size), task_idx, :]
                # 将掩码应用到卫星选择logits上
                # 对于不可选择的卫星（mask=0），设置为很大的负数
                sat_logits = sat_logits + (sat_mask + 1e-10).log()
            
            sat_probs = F.softmax(sat_logits, dim=1)
            
            if self.training:
                sat_dist = torch.distributions.Categorical(sat_probs)
                sat_idx = sat_dist.sample()
                sat_logp = sat_dist.log_prob(sat_idx)
            else:
                _, sat_idx = torch.max(sat_probs, 1)
                sat_logp = torch.log(torch.gather(sat_probs, 1, sat_idx.unsqueeze(1))).squeeze(1)
            
            # 记录结果
            tour_indices.append(task_idx)
            satellite_indices.append(sat_idx)
            tour_logp.append(task_logp)
            satellite_logp.append(sat_logp)
            
            # 更新动态状态
            if self.update_fn is not None:
                dynamic = self.update_fn(static, dynamic, task_idx, sat_idx)
            
            # 更新掩码
            if self.mask_fn is not None:
                mask_result = self.mask_fn(dynamic, static=static)
                if isinstance(mask_result, tuple):
                    mask, satellite_masks = mask_result
                else:
                    mask = mask_result
                    satellite_masks = None
            
            # 更新解码器输入
            decoder_input = torch.gather(
                static, 2,
                task_idx.view(-1, 1, 1).expand(-1, self.static_size, 1)
            ).detach()
            decoder_hidden = self.decoder(decoder_input)
        
        # 转换为张量
        tour_indices = torch.stack(tour_indices, dim=1)
        satellite_indices = torch.stack(satellite_indices, dim=1)
        tour_logp = torch.stack(tour_logp, dim=1)
        satellite_logp = torch.stack(satellite_logp, dim=1)
        
        return tour_indices, satellite_indices, tour_logp, satellite_logp


class ConstellationStateCriticPN(nn.Module):
    """
    基于PN的星座状态评论家，评估星座级别的问题复杂度
    """
    def __init__(self, static_size, dynamic_size, hidden_size, num_satellites, 
                 constellation_mode='cooperative'):
        super(ConstellationStateCriticPN, self).__init__()
        self.num_satellites = num_satellites
        self.constellation_mode = constellation_mode
        
        # 使用与策略网络相同的编码器
        self.constellation_encoder = ConstellationEncoder(
            static_size + dynamic_size,
            hidden_size,
            num_satellites,
            constellation_mode,
            use_transformer=False,  # PN模型不使用Transformer
            transformer_config=None
        )
        
        # 评论家网络
        self.fc1 = nn.Conv1d(hidden_size, 20, kernel_size=1)
        self.fc2 = nn.Conv1d(20, 20, kernel_size=1)
        self.fc3 = nn.Conv1d(20, 1, kernel_size=1)
        
        # 初始化参数
        for p in self.parameters():
            if len(p.shape) > 1:
                nn.init.xavier_uniform_(p)

    def forward(self, static, dynamic):
        # 获取星座级别的特征
        constellation_features, _ = self.constellation_encoder(static, dynamic)
        
        # 评估问题复杂度
        output = F.relu(self.fc1(constellation_features))
        output = F.relu(self.fc2(output))
        output = self.fc3(output).sum(dim=2)
        
        return output
