"""
专门诊断掩码机制和资源约束的问题
"""
import torch
import numpy as np
import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from constellation_smp.constellation_smp import ConstellationSMPDataset
from hyperparameter import args

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

def test_mask_mechanism():
    """测试掩码机制"""
    print("=" * 60)
    print("掩码机制诊断")
    print("=" * 60)
    
    dataset = ConstellationSMPDataset(
        size=5,
        num_samples=1,
        seed=12345,
        memory_total=0.3,
        power_total=5,
        num_satellites=3
    )
    
    static, dynamic, _ = dataset[0]
    static = static.unsqueeze(0)
    dynamic = dynamic.unsqueeze(0)
    
    print("初始资源状态:")
    for sat_idx in range(3):
        print(f"  卫星{sat_idx}:")
        print(f"    内存: {dynamic[0, 2, :, sat_idx]}")
        print(f"    能量: {dynamic[0, 3, :, sat_idx]}")
    print()
    
    # 测试初始掩码
    mask, satellite_masks = dataset.update_mask(dynamic, static=static)
    
    print("初始掩码状态:")
    print(f"  综合掩码: {mask[0]}")
    print("  各卫星掩码:")
    for sat_idx in range(3):
        print(f"    卫星{sat_idx}: {satellite_masks[0, :, sat_idx]}")
    print()
    
    # 分析每个任务的资源需求
    print("任务资源需求分析:")
    for task_idx in range(1, static.size(2)):  # 跳过起始点
        memory_need = static[0, 5, task_idx]
        power_need = static[0, 6, task_idx]
        is_station = static[0, 7, task_idx] if static.size(1) > 7 else 0
        
        print(f"  任务{task_idx}: 内存需求={memory_need:.4f}, 能量需求={power_need:.4f}, Station={is_station}")
        
        # 检查每颗卫星是否能执行此任务
        for sat_idx in range(3):
            sat_memory = dynamic[0, 2, task_idx, sat_idx]
            sat_power = dynamic[0, 3, task_idx, sat_idx]
            
            memory_ok = sat_memory >= memory_need if memory_need > 0 else True
            power_ok = sat_power >= power_need if power_need > 0 else True
            mask_ok = satellite_masks[0, task_idx, sat_idx] > 0
            
            status = "✓" if (memory_ok and power_ok and mask_ok) else "✗"
            print(f"    卫星{sat_idx}: 内存{sat_memory:.4f}{'≥' if memory_ok else '<'}{memory_need:.4f}, "
                  f"能量{sat_power:.4f}{'≥' if power_ok else '<'}{power_need:.4f}, "
                  f"掩码{'✓' if mask_ok else '✗'} {status}")
    print()
    
    return static, dynamic, mask, satellite_masks

def test_resource_constraint_violation():
    """测试资源约束违反的情况"""
    print("=" * 60)
    print("资源约束违反测试")
    print("=" * 60)
    
    dataset = ConstellationSMPDataset(
        size=5,
        num_samples=1,
        seed=12345,
        memory_total=0.3,
        power_total=5,
        num_satellites=3
    )
    
    static, dynamic, _ = dataset[0]
    static = static.unsqueeze(0)
    dynamic = dynamic.unsqueeze(0)
    
    # 人为降低某颗卫星的资源
    print("人为降低卫星0的资源:")
    original_memory = dynamic[0, 2, :, 0].clone()
    original_power = dynamic[0, 3, :, 0].clone()
    
    # 将卫星0的内存和能量设为很低的值
    dynamic[0, 2, :, 0] = 0.001  # 很少的内存
    dynamic[0, 3, :, 0] = 0.001  # 很少的能量
    
    print(f"  原始内存: {original_memory}")
    print(f"  修改后内存: {dynamic[0, 2, :, 0]}")
    print(f"  原始能量: {original_power}")
    print(f"  修改后能量: {dynamic[0, 3, :, 0]}")
    print()
    
    # 重新计算掩码
    mask, satellite_masks = dataset.update_mask(dynamic, static=static)
    
    print("资源约束后的掩码:")
    print(f"  综合掩码: {mask[0]}")
    print("  各卫星掩码:")
    for sat_idx in range(3):
        print(f"    卫星{sat_idx}: {satellite_masks[0, :, sat_idx]}")
    print()
    
    # 检查卫星0是否被正确屏蔽
    sat0_blocked_tasks = torch.sum(satellite_masks[0, 1:, 0] == 0)  # 跳过起始点
    print(f"卫星0被屏蔽的任务数: {sat0_blocked_tasks.item()}")
    
    if sat0_blocked_tasks > 0:
        print("✓ 资源约束正确生效，资源不足的卫星被屏蔽")
    else:
        print("✗ 资源约束可能没有正确生效")
    
    return mask, satellite_masks

def test_station_task_handling():
    """测试Station任务的特殊处理"""
    print("=" * 60)
    print("Station任务处理测试")
    print("=" * 60)
    
    dataset = ConstellationSMPDataset(
        size=10,  # 更大的数据集，更可能包含station
        num_samples=1,
        seed=12345,
        memory_total=0.3,
        power_total=5,
        num_satellites=3
    )
    
    static, dynamic, _ = dataset[0]
    static = static.unsqueeze(0)
    dynamic = dynamic.unsqueeze(0)
    
    # 找到station任务
    if static.size(1) > 7:
        station_indices = torch.where(static[0, 7, :] == 1)[0]
        print(f"发现Station任务: {station_indices.tolist()}")
        
        for station_idx in station_indices:
            print(f"\nStation任务 {station_idx} 分析:")
            print(f"  收益: {static[0, 4, station_idx]:.3f}")
            print(f"  内存消耗: {static[0, 5, station_idx]:.3f} (负值表示提供内存)")
            print(f"  能量消耗: {static[0, 6, station_idx]:.3f}")
            
            # 模拟执行station任务
            chosen_idx = torch.tensor([station_idx])
            satellite_idx = torch.tensor([0])  # 让卫星0执行
            
            print(f"\n执行前卫星0的资源:")
            print(f"  内存: {dynamic[0, 2, :, 0]}")
            print(f"  能量: {dynamic[0, 3, :, 0]}")
            
            # 更新动态状态
            new_dynamic = dataset.update_dynamic(static, dynamic, chosen_idx, satellite_idx)
            
            print(f"\n执行后卫星0的资源:")
            print(f"  内存: {new_dynamic[0, 2, :, 0]}")
            print(f"  能量: {new_dynamic[0, 3, :, 0]}")
            
            # 检查内存是否增加
            memory_change = new_dynamic[0, 2, 0, 0] - dynamic[0, 2, 0, 0]
            print(f"\n内存变化: {memory_change:.4f}")
            
            if memory_change > 0:
                print("✓ Station任务正确增加了卫星内存")
            else:
                print("✗ Station任务没有正确增加内存")
    else:
        print("当前数据集没有Station标记")

def main():
    """主诊断函数"""
    print("🔍 掩码机制和资源约束诊断开始...")
    print()
    
    # 1. 测试掩码机制
    test_mask_mechanism()
    
    # 2. 测试资源约束违反
    test_resource_constraint_violation()
    
    # 3. 测试Station任务处理
    test_station_task_handling()
    
    print("=" * 60)
    print("掩码机制诊断完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
