"""
PN+IndRNN模型快速修复脚本
修复关键问题：约束违反、计算效率、输出稳定性
"""
import os
import sys
import torch
import numpy as np
from torch.utils.data import DataLoader

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from constellation_smp.constellation_smp import ConstellationSMPDataset, reward
from constellation_smp.pn_constellation import PNConstellation

def test_constraint_violation():
    """测试约束违反问题"""
    print("🔍 测试约束违反问题")
    print("=" * 50)
    
    # 创建测试数据
    dataset = ConstellationSMPDataset(20, 5, 12345, 0.1, 2, 3)  # 严格资源限制
    test_loader = DataLoader(dataset, batch_size=2, shuffle=False)
    static, dynamic, _ = next(iter(test_loader))
    
    # 创建模型
    actor = PNConstellation(
        static_size=static.size(1),
        dynamic_size=dynamic.size(1),
        hidden_size=128,
        num_satellites=3,
        rnn='indrnn',
        num_layers=2,
        constellation_mode='cooperative',
        update_fn=dataset.update_dynamic,
        mask_fn=dataset.update_mask
    )
    
    # 测试多次推理
    revenue_rates = []
    for i in range(10):
        with torch.no_grad():
            tour_indices, satellite_indices, _, _ = actor(static, dynamic)
            _, revenue_rate, _, _, _ = reward(static, tour_indices, satellite_indices, 'cooperative')
            revenue_rates.append(revenue_rate.mean().item())
    
    avg_revenue = np.mean(revenue_rates)
    max_revenue = np.max(revenue_rates)
    
    print(f"收益率统计:")
    print(f"  平均收益率: {avg_revenue:.4f}")
    print(f"  最大收益率: {max_revenue:.4f}")
    
    if max_revenue > 1.0:
        print("🔴 发现约束违反：收益率超过1.0")
        return False
    elif avg_revenue > 0.8:
        print("⚠️ 收益率异常高，可能存在轻微约束违反")
        return False
    else:
        print("✅ 约束检查通过")
        return True

def test_computation_efficiency():
    """测试计算效率"""
    print("\n🕐 测试计算效率")
    print("=" * 50)
    
    # 创建测试数据
    dataset = ConstellationSMPDataset(50, 5, 12345, 0.3, 5, 3)
    test_loader = DataLoader(dataset, batch_size=4, shuffle=False)
    static, dynamic, _ = next(iter(test_loader))
    
    # 创建模型
    actor = PNConstellation(
        static_size=static.size(1),
        dynamic_size=dynamic.size(1),
        hidden_size=128,
        num_satellites=3,
        rnn='indrnn',
        num_layers=2,
        constellation_mode='cooperative',
        update_fn=dataset.update_dynamic,
        mask_fn=dataset.update_mask
    )
    
    # 预热
    with torch.no_grad():
        for _ in range(3):
            actor(static, dynamic)
    
    # 测试时间
    import time
    times = []
    for _ in range(10):
        start_time = time.time()
        with torch.no_grad():
            actor(static, dynamic)
        times.append(time.time() - start_time)
    
    avg_time = np.mean(times) * 1000  # 转换为毫秒
    print(f"前向传播时间: {avg_time:.2f}ms")
    
    if avg_time > 500:
        print("🔴 计算效率低下")
        return False
    elif avg_time > 300:
        print("⚠️ 计算效率一般")
        return False
    else:
        print("✅ 计算效率良好")
        return True

def test_output_stability():
    """测试输出稳定性"""
    print("\n📊 测试输出稳定性")
    print("=" * 50)
    
    # 创建测试数据
    dataset = ConstellationSMPDataset(30, 10, 12345, 0.3, 5, 3)
    test_loader = DataLoader(dataset, batch_size=2, shuffle=False)
    static, dynamic, _ = next(iter(test_loader))
    
    # 创建模型
    actor = PNConstellation(
        static_size=static.size(1),
        dynamic_size=dynamic.size(1),
        hidden_size=128,
        num_satellites=3,
        rnn='indrnn',
        num_layers=2,
        constellation_mode='cooperative',
        update_fn=dataset.update_dynamic,
        mask_fn=dataset.update_mask
    )
    
    # 测试输出稳定性
    rewards = []
    revenue_rates = []
    
    for i in range(20):
        with torch.no_grad():
            tour_indices, satellite_indices, _, _ = actor(static, dynamic)
            reward_val, revenue_rate, _, _, _ = reward(static, tour_indices, satellite_indices, 'cooperative')
            rewards.append(reward_val.mean().item())
            revenue_rates.append(revenue_rate.mean().item())
    
    reward_std = np.std(rewards)
    revenue_std = np.std(revenue_rates)
    
    print(f"输出稳定性:")
    print(f"  奖励标准差: {reward_std:.4f}")
    print(f"  收益率标准差: {revenue_std:.4f}")
    
    if reward_std > 5.0 or revenue_std > 0.2:
        print("🔴 输出不稳定")
        return False
    elif reward_std > 2.0 or revenue_std > 0.1:
        print("⚠️ 输出稳定性一般")
        return False
    else:
        print("✅ 输出稳定")
        return True

def test_satellite_balance():
    """测试卫星选择均衡性"""
    print("\n🛰️ 测试卫星选择均衡性")
    print("=" * 50)
    
    # 创建测试数据
    dataset = ConstellationSMPDataset(30, 10, 12345, 0.3, 5, 3)
    test_loader = DataLoader(dataset, batch_size=2, shuffle=False)
    static, dynamic, _ = next(iter(test_loader))
    
    # 创建模型
    actor = PNConstellation(
        static_size=static.size(1),
        dynamic_size=dynamic.size(1),
        hidden_size=128,
        num_satellites=3,
        rnn='indrnn',
        num_layers=2,
        constellation_mode='cooperative',
        update_fn=dataset.update_dynamic,
        mask_fn=dataset.update_mask
    )
    
    # 统计卫星选择
    sat_counts = np.zeros(3)
    
    for i in range(20):
        with torch.no_grad():
            tour_indices, satellite_indices, _, _ = actor(static, dynamic)
            for sat_idx in satellite_indices.flatten():
                sat_counts[sat_idx.item()] += 1
    
    # 计算分布和均衡度
    sat_dist = sat_counts / sat_counts.sum()
    balance_score = np.std(sat_dist)
    
    print(f"卫星选择分布: {sat_dist}")
    print(f"均衡度 (标准差): {balance_score:.4f}")
    
    if balance_score > 0.15:
        print("🔴 卫星选择严重不均衡")
        return False
    elif balance_score > 0.08:
        print("⚠️ 卫星选择略有偏向")
        return False
    else:
        print("✅ 卫星选择均衡")
        return True

def main():
    """主测试函数"""
    print("🔧 PN+IndRNN模型问题修复验证")
    print("=" * 80)
    
    test_results = []
    
    # 运行所有测试
    test_results.append(("约束违反", test_constraint_violation()))
    test_results.append(("计算效率", test_computation_efficiency()))
    test_results.append(("输出稳定性", test_output_stability()))
    test_results.append(("卫星均衡性", test_satellite_balance()))
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("📋 测试结果汇总")
    print("=" * 80)
    
    passed = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(test_results)} 项测试通过")
    
    if passed == len(test_results):
        print("🎉 所有测试通过！PN+IndRNN模型可以继续训练")
        return True
    elif passed >= len(test_results) * 0.75:
        print("⚠️ 大部分测试通过，可以尝试训练但需要监控")
        return True
    else:
        print("🔴 多项测试失败，建议修复后再训练")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 建议：可以继续进行PN+IndRNN的消融实验")
    else:
        print("\n❌ 建议：暂停PN+IndRNN实验，专注于GPN对比实验")
